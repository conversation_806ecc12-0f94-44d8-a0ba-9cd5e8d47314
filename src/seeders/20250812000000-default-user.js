'use strict';
const bcrypt = require('bcrypt');
const { User } = require('../models');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await User.create({
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin',
      password: 'password123',
    })
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('Users', {
      email: '<EMAIL>',
    });
  },
};