const ApiController = require('./ApiController');
const AuthService = require('../services/AuthService');

class AuthController extends ApiController {
  constructor() {
    super();
    this.service = new AuthService();
    this.login = this.login.bind(this);
  }

  /**
   * Handle user login request
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async login(req, res, next) {
    try {
      // TODO: Input validation here
      const result = await this.service.login(req.body);

      const output = {
        user: result.user,
        authToken: result.authToken,
      };

      // TODO: Output class object here
      res.status(200).json({ data: output });
    } catch (error) {
      super.handleError(error, res, next);
    }
  }
}

module.exports = new AuthController();
