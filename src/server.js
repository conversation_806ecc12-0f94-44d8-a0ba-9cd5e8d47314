const app = require('./app');
const config = require('./config/config');

const PORT = config.port;
const HOST = config.host;

// Graceful shutdown handler
const gracefulShutdown = (server, signal) => {
  console.log(`\nReceived ${signal}. Starting graceful shutdown...`);

  server.close((err) => {
    if (err) {
      console.error('Error during server shutdown:', err);
      process.exit(1);
    }

    console.log('Server closed successfully.');

    // Close database connections
    const { sequelize } = require('./config/sequelize');
    sequelize.close().then(() => {
      console.log('Database connections closed.');
      process.exit(0);
    }).catch((error) => {
      console.error('Error closing database connections:', error);
      process.exit(1);
    });
  });
};

// Start server
const startServer = async () => {
  try {
    const server = app.listen(PORT, HOST, () => {
      console.log(`🚀 Server is running on http://${HOST}:${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🔍 Health check: http://${HOST}:${PORT}/api/v1/health`);
    });

    // Handle graceful shutdown
    process.on('SIGTERM', () => gracefulShutdown(server, 'SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown(server, 'SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      gracefulShutdown(server, 'UNCAUGHT_EXCEPTION');
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      gracefulShutdown(server, 'UNHANDLED_REJECTION');
    });

    return server;
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Start the server if this file is run directly
if (require.main === module) {
  startServer();
}

module.exports = { startServer };
