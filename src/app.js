const express = require('express');
const cors = require('cors');
require('dotenv').config();

// Import middleware
const securityHeaders = require('./middlewares/securityHeaders');

// Import routes
const authRoutes = require('./routes/auth');

const app = express();

// Security headers middleware
app.use(securityHeaders);

// CORS configuration
app.use(cors({
  origin: process.env.CORS_ORIGIN || '*',
  credentials: true,
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/api/v1/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
  });
});

// API routes
app.use('/api/v1/auth', authRoutes);

// TODO: 404 handler for undefined routes

module.exports = app;
