class ApiController {
  /**
   * Handle errors in a consistent way across all controllers.
   * @param {Error} error - The error object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  handleError(error, res, next) {
    console.error('Controller error:', error);

    // If response has already been sent, pass to error handler middleware
    if (res.headersSent) { return next(error); }

    const showBacktrace = process.env.NODE_ENV !== 'production';
    res.status(500).json({
      error: 'Internal server error',
      detail: showBacktrace ? error.message : undefined,
      backtrace: showBacktrace ? error.stack : undefined,
    });
  }
}

module.exports = ApiController;
